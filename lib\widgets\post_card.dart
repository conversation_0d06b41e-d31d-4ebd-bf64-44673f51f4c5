import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:share_plus/share_plus.dart';
import '../constants/app_constants.dart';
import '../models/post_model.dart';
import '../providers/auth_provider.dart';
import '../services/post_service.dart';
import '../screens/user_profile_screen.dart';
import '../screens/comments_screen.dart';
import '../screens/edit_post_screen.dart';
import '../widgets/role_indicator.dart';
import '../services/user_service.dart';
import '../models/user_model.dart';

class PostCard extends StatefulWidget {
  final PostModel post;
  final bool showUserInfo;
  final VoidCallback? onPostDeleted;
  final String? currentUserId;

  const PostCard({
    super.key,
    required this.post,
    this.showUserInfo = true,
    this.onPostDeleted,
    this.currentUserId,
  });

  @override
  State<PostCard> createState() => _PostCardState();
}

class _PostCardState extends State<PostCard> {
  late PostModel _currentPost;
  UserModel? _postUser;

  @override
  void initState() {
    super.initState();
    _currentPost = widget.post;
    _loadPostUser();
  }

  Future<void> _loadPostUser() async {
    try {
      final user = await UserService.getUserById(_currentPost.userId);
      if (mounted) {
        setState(() {
          _postUser = user;
        });
      }
    } catch (e) {
      // Handle error silently, user will just not see role indicator
    }
  }

  @override
  void didUpdateWidget(PostCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.post != widget.post) {
      _currentPost = widget.post;
      _loadPostUser(); // Reload user data for new post
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingMedium,
        vertical: AppConstants.paddingSmall,
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // User Info (conditional)
            if (widget.showUserInfo) ...[
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: AppConstants.primaryColor,
                    ),
                    child: ClipOval(
                      child: _currentPost.userProfileImageUrl != null && _currentPost.userProfileImageUrl!.isNotEmpty
                          ? CachedNetworkImage(
                              imageUrl: _currentPost.userProfileImageUrl!,
                              width: 40,
                              height: 40,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                color: AppConstants.primaryColor,
                                child: Center(
                                  child: Text(
                                    _currentPost.userDisplayName.isNotEmpty
                                        ? _currentPost.userDisplayName[0].toUpperCase()
                                        : 'U',
                                    style: const TextStyle(
                                      color: AppConstants.onPrimaryColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                color: AppConstants.primaryColor,
                                child: Center(
                                  child: Text(
                                    _currentPost.userDisplayName.isNotEmpty
                                        ? _currentPost.userDisplayName[0].toUpperCase()
                                        : 'U',
                                    style: const TextStyle(
                                      color: AppConstants.onPrimaryColor,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 16,
                                    ),
                                  ),
                                ),
                              ),
                            )
                          : Container(
                              color: AppConstants.primaryColor,
                              child: Center(
                                child: Text(
                                  _currentPost.userDisplayName.isNotEmpty
                                      ? _currentPost.userDisplayName[0].toUpperCase()
                                      : 'U',
                                  style: const TextStyle(
                                    color: AppConstants.onPrimaryColor,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ),
                            ),
                    ),
                  ),
                  const SizedBox(width: AppConstants.paddingMedium),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        GestureDetector(
                          onTap: () => _navigateToUserProfile(context, _currentPost.userId, _currentPost.userDisplayName),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Text(
                                _currentPost.userDisplayName,
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: AppConstants.primaryColor,
                                ),
                              ),
                              if (_postUser != null) ...[
                                const SizedBox(width: 6),
                                RoleIndicator(
                                  user: _postUser!,
                                  size: 14,
                                ),
                              ],
                            ],
                          ),
                        ),
                        Text(
                          _currentPost.timeAgo,
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    ),
                  ),
                  Consumer<AuthProvider>(
                    builder: (context, authProvider, child) {
                      final currentUser = authProvider.currentUser;
                      if (currentUser?.id == _currentPost.userId) {
                        return IconButton(
                          icon: const Icon(Icons.more_vert),
                          onPressed: () => _showPostOptions(context, _currentPost),
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                ],
              ),
              const SizedBox(height: AppConstants.paddingMedium),
            ],

            // Post Content
            if (_currentPost.content.isNotEmpty)
              Text(
                _currentPost.content,
                style: Theme.of(context).textTheme.bodyLarge,
              ),

            // Post Images
            if (_currentPost.hasImages) ...[
              const SizedBox(height: AppConstants.paddingMedium),
              _buildPostImages(context, _currentPost.imageUrls),
            ],

            const SizedBox(height: AppConstants.paddingMedium),

            // Post Actions
            Consumer<AuthProvider>(
              builder: (context, authProvider, child) {
                final currentUser = authProvider.currentUser;
                final isLiked = currentUser != null && _currentPost.isLikedBy(currentUser.id);

                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    TextButton.icon(
                      onPressed: currentUser != null
                          ? () => _toggleLike(context, _currentPost, currentUser.id)
                          : null,
                      icon: Icon(
                        isLiked ? Icons.favorite : Icons.favorite_border,
                        color: isLiked ? Colors.red : null,
                      ),
                      label: Text('${_currentPost.likeCount}'),
                    ),
                    TextButton.icon(
                      onPressed: () => _navigateToComments(context, _currentPost),
                      icon: const Icon(Icons.comment_outlined),
                      label: Text('${_currentPost.commentCount}'),
                    ),
                    TextButton.icon(
                      onPressed: () => _sharePost(context, _currentPost),
                      icon: const Icon(Icons.share_outlined),
                      label: const Text('Share'),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPostImages(BuildContext context, List<String> imageUrls) {
    if (imageUrls.length == 1) {
      return _buildSingleImage(context, imageUrls.first);
    } else if (imageUrls.length == 2) {
      return _buildTwoImages(context, imageUrls);
    } else if (imageUrls.length == 3) {
      return _buildThreeImages(context, imageUrls);
    } else if (imageUrls.length == 4) {
      return _buildFourImages(context, imageUrls);
    } else {
      return _buildMultipleImages(context, imageUrls);
    }
  }

  Widget _buildSingleImage(BuildContext context, String imageUrl) {
    return GestureDetector(
      onTap: () => _showImageGallery(context, [imageUrl], 0),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Container(
          constraints: const BoxConstraints(
            maxHeight: 400,
            minHeight: 200,
          ),
          child: CachedNetworkImage(
            imageUrl: imageUrl,
            width: double.infinity,
            fit: BoxFit.contain,
            placeholder: (context, url) => Container(
              height: 250,
              color: Colors.grey.shade100,
              child: const Center(
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                ),
              ),
            ),
            errorWidget: (context, url, error) => Container(
              height: 250,
              color: Colors.grey.shade100,
              child: const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.broken_image, size: 48, color: Colors.grey),
                  SizedBox(height: 8),
                  Text('Failed to load image', style: TextStyle(color: Colors.grey)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTwoImages(BuildContext context, List<String> imageUrls) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: SizedBox(
        height: 250,
        child: Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () => _showImageGallery(context, imageUrls, 0),
                child: _buildImageContainer(imageUrls[0], height: 250),
              ),
            ),
            const SizedBox(width: 2),
            Expanded(
              child: GestureDetector(
                onTap: () => _showImageGallery(context, imageUrls, 1),
                child: _buildImageContainer(imageUrls[1], height: 250),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThreeImages(BuildContext context, List<String> imageUrls) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: SizedBox(
        height: 250,
        child: Row(
          children: [
            Expanded(
              flex: 2,
              child: GestureDetector(
                onTap: () => _showImageGallery(context, imageUrls, 0),
                child: _buildImageContainer(imageUrls[0], height: 250),
              ),
            ),
            const SizedBox(width: 2),
            Expanded(
              child: Column(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _showImageGallery(context, imageUrls, 1),
                      child: _buildImageContainer(imageUrls[1], height: 124),
                    ),
                  ),
                  const SizedBox(height: 2),
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _showImageGallery(context, imageUrls, 2),
                      child: _buildImageContainer(imageUrls[2], height: 124),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFourImages(BuildContext context, List<String> imageUrls) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: SizedBox(
        height: 250,
        child: Column(
          children: [
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _showImageGallery(context, imageUrls, 0),
                      child: _buildImageContainer(imageUrls[0], height: 124),
                    ),
                  ),
                  const SizedBox(width: 2),
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _showImageGallery(context, imageUrls, 1),
                      child: _buildImageContainer(imageUrls[1], height: 124),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 2),
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _showImageGallery(context, imageUrls, 2),
                      child: _buildImageContainer(imageUrls[2], height: 124),
                    ),
                  ),
                  const SizedBox(width: 2),
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _showImageGallery(context, imageUrls, 3),
                      child: _buildImageContainer(imageUrls[3], height: 124),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMultipleImages(BuildContext context, List<String> imageUrls) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(12),
      child: SizedBox(
        height: 250,
        child: Column(
          children: [
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _showImageGallery(context, imageUrls, 0),
                      child: _buildImageContainer(imageUrls[0], height: 124),
                    ),
                  ),
                  const SizedBox(width: 2),
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _showImageGallery(context, imageUrls, 1),
                      child: _buildImageContainer(imageUrls[1], height: 124),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 2),
            Expanded(
              child: Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _showImageGallery(context, imageUrls, 2),
                      child: _buildImageContainer(imageUrls[2], height: 124),
                    ),
                  ),
                  const SizedBox(width: 2),
                  Expanded(
                    child: GestureDetector(
                      onTap: () => _showImageGallery(context, imageUrls, 3),
                      child: Stack(
                        children: [
                          _buildImageContainer(imageUrls[3], height: 124),
                          if (imageUrls.length > 4)
                            Container(
                              height: 124,
                              color: Colors.black54,
                              child: Center(
                                child: Text(
                                  '+${imageUrls.length - 4}',
                                  style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageContainer(String imageUrl, {required double height}) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      height: height,
      width: double.infinity,
      fit: BoxFit.cover,
      placeholder: (context, url) => Container(
        height: height,
        color: Colors.grey.shade100,
        child: const Center(
          child: CircularProgressIndicator(strokeWidth: 2),
        ),
      ),
      errorWidget: (context, url, error) => Container(
        height: height,
        color: Colors.grey.shade100,
        child: const Icon(Icons.broken_image, color: Colors.grey),
      ),
    );
  }

  void _showFullScreenImage(BuildContext context, String imageUrl) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            iconTheme: const IconThemeData(color: Colors.white),
          ),
          body: Center(
            child: InteractiveViewer(
              child: CachedNetworkImage(
                imageUrl: imageUrl,
                fit: BoxFit.contain,
                placeholder: (context, url) => const Center(
                  child: CircularProgressIndicator(
                    color: Colors.white,
                  ),
                ),
                errorWidget: (context, url, error) => const Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.broken_image, size: 64, color: Colors.white),
                    SizedBox(height: 16),
                    Text(
                      'Failed to load image',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _showImageGallery(BuildContext context, List<String> imageUrls, int initialIndex) {
    int currentIndex = initialIndex;
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => StatefulBuilder(
          builder: (context, setState) => Scaffold(
            backgroundColor: Colors.black,
            appBar: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              iconTheme: const IconThemeData(color: Colors.white),
              title: Text(
                '${currentIndex + 1} / ${imageUrls.length}',
                style: const TextStyle(color: Colors.white),
              ),
            ),
            body: PageView.builder(
              controller: PageController(initialPage: initialIndex),
              itemCount: imageUrls.length,
              onPageChanged: (index) {
                setState(() {
                  currentIndex = index;
                });
              },
              itemBuilder: (context, index) {
                return Center(
                  child: InteractiveViewer(
                    child: CachedNetworkImage(
                      imageUrl: imageUrls[index],
                      fit: BoxFit.contain,
                      placeholder: (context, url) => const Center(
                        child: CircularProgressIndicator(
                          color: Colors.white,
                        ),
                      ),
                      errorWidget: (context, url, error) => const Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.broken_image, size: 64, color: Colors.white),
                          SizedBox(height: 16),
                          Text(
                            'Failed to load image',
                            style: TextStyle(color: Colors.white),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToUserProfile(BuildContext context, String userId, String displayName) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => UserProfileScreen(
          userId: userId,
          initialUserName: displayName,
        ),
      ),
    );
  }

  void _navigateToComments(BuildContext context, PostModel post) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CommentsScreen(post: post),
      ),
    );
  }

  Future<void> _toggleLike(BuildContext context, PostModel post, String userId) async {
    try {
      // Optimistically update UI
      setState(() {
        List<String> newLikes = List.from(_currentPost.likes);
        if (newLikes.contains(userId)) {
          newLikes.remove(userId);
        } else {
          newLikes.add(userId);
        }
        _currentPost = _currentPost.copyWith(likes: newLikes);
      });

      // Update in backend
      final success = await PostService.toggleLike(post.id, userId);

      if (!success) {
        // Revert if failed
        setState(() {
          List<String> revertLikes = List.from(_currentPost.likes);
          if (revertLikes.contains(userId)) {
            revertLikes.remove(userId);
          } else {
            revertLikes.add(userId);
          }
          _currentPost = _currentPost.copyWith(likes: revertLikes);
        });

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to update like'),
              backgroundColor: AppConstants.errorColor,
            ),
          );
        }
      }
    } catch (e) {
      // Revert on error
      setState(() {
        List<String> revertLikes = List.from(_currentPost.likes);
        if (revertLikes.contains(userId)) {
          revertLikes.remove(userId);
        } else {
          revertLikes.add(userId);
        }
        _currentPost = _currentPost.copyWith(likes: revertLikes);
      });

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: ${e.toString()}'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  void _showPostOptions(BuildContext context, PostModel post) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit, color: AppConstants.primaryColor),
              title: const Text('Edit Post'),
              onTap: () {
                Navigator.pop(context);
                _editPost(context, post);
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text('Delete Post'),
              onTap: () {
                Navigator.pop(context);
                _confirmDeletePost(context, post);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _confirmDeletePost(BuildContext context, PostModel post) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Post'),
        content: const Text('Are you sure you want to delete this post?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await PostService.deletePost(post.id);
                if (widget.onPostDeleted != null) {
                  widget.onPostDeleted!();
                }
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Post deleted successfully')),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error deleting post: ${e.toString()}'),
                      backgroundColor: AppConstants.errorColor,
                    ),
                  );
                }
              }
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _sharePost(BuildContext context, PostModel post) async {
    try {
      String shareText = '${post.userDisplayName} shared a post:\n\n${post.content}';

      if (post.hasImages) {
        shareText += '\n\nPost includes ${post.imageUrls.length} image(s).';
      }

      shareText += '\n\nShared via Amal Point App';

      await Share.share(
        shareText,
        subject: 'Check out this post from ${post.userDisplayName}',
      );
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error sharing post: ${e.toString()}'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  void _editPost(BuildContext context, PostModel post) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EditPostScreen(post: post),
      ),
    );

    // If post was updated successfully, refresh the feed
    if (result == true && widget.onPostDeleted != null) {
      widget.onPostDeleted!(); // This callback can be used for refreshing
    }
  }
}
