import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import '../enums/user_role.dart';
import '../constants/app_constants.dart';

class AuthService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get current user
  User? get currentUser => _auth.currentUser;

  // Get current user stream
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign in with email and password (Admin only)
  Future<UserModel?> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      // Check if email is in admin list
      if (!AppConstants.adminEmails.contains(email.toLowerCase())) {
        throw Exception('Access denied. Admin privileges required.');
      }

      final UserCredential result = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (result.user != null) {
        final userModel = await getUserById(result.user!.uid);
        
        // Verify user has admin role
        if (userModel == null || !userModel.isAdmin) {
          await _auth.signOut();
          throw Exception('Access denied. Admin privileges required.');
        }

        return userModel;
      }
      return null;
    } catch (e) {
      throw Exception('Sign in failed: ${e.toString()}');
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      throw Exception('Sign out failed: ${e.toString()}');
    }
  }

  // Get user by ID
  Future<UserModel?> getUserById(String userId) async {
    try {
      final DocumentSnapshot doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (doc.exists) {
        return UserModel.fromDocument(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user: ${e.toString()}');
    }
  }

  // Update user role
  Future<void> updateUserRole({
    required String userId,
    required UserRole newRole,
  }) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'role': newRole.value,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to update user role: ${e.toString()}');
    }
  }

  // Update reseller application status
  Future<void> updateResellerApplicationStatus({
    required String userId,
    required ResellerApplicationStatus status,
  }) async {
    try {
      final Map<String, dynamic> updateData = {
        'resellerApplicationStatus': status.value,
        'updatedAt': Timestamp.now(),
      };

      // If approved, also update role to reseller
      if (status == ResellerApplicationStatus.approved) {
        updateData['role'] = UserRole.reseller.value;
      }

      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update(updateData);
    } catch (e) {
      throw Exception('Failed to update reseller application status: ${e.toString()}');
    }
  }

  // Deactivate user
  Future<void> deactivateUser(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'isActive': false,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to deactivate user: ${e.toString()}');
    }
  }

  // Activate user
  Future<void> activateUser(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'isActive': true,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to activate user: ${e.toString()}');
    }
  }

  // Verify user
  Future<void> verifyUser(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'isVerified': true,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to verify user: ${e.toString()}');
    }
  }

  // Unverify user
  Future<void> unverifyUser(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'isVerified': false,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to unverify user: ${e.toString()}');
    }
  }

  // Get all users with pagination
  Future<List<UserModel>> getUsers({
    int limit = 20,
    DocumentSnapshot? lastDocument,
    UserRole? roleFilter,
    bool? isActiveFilter,
  }) async {
    try {
      Query query = _firestore
          .collection(AppConstants.usersCollection)
          .orderBy('createdAt', descending: true);

      // Apply filters
      if (roleFilter != null) {
        query = query.where('role', isEqualTo: roleFilter.value);
      }
      if (isActiveFilter != null) {
        query = query.where('isActive', isEqualTo: isActiveFilter);
      }

      // Apply pagination
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      query = query.limit(limit);

      final QuerySnapshot querySnapshot = await query.get();
      return querySnapshot.docs
          .map((doc) => UserModel.fromDocument(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get users: ${e.toString()}');
    }
  }

  // Search users
  Future<List<UserModel>> searchUsers({
    required String searchTerm,
    int limit = 20,
  }) async {
    try {
      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .limit(limit * 3) // Get more to filter locally
          .get();

      final List<UserModel> allUsers = querySnapshot.docs
          .map((doc) => UserModel.fromDocument(doc))
          .toList();

      // Filter locally for better search
      final searchLower = searchTerm.toLowerCase();
      return allUsers.where((user) {
        return user.username.toLowerCase().contains(searchLower) ||
               user.displayName.toLowerCase().contains(searchLower) ||
               user.email.toLowerCase().contains(searchLower);
      }).take(limit).toList();
    } catch (e) {
      throw Exception('Failed to search users: ${e.toString()}');
    }
  }

  // Get pending reseller applications
  Future<List<UserModel>> getPendingResellerApplications() async {
    try {
      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('resellerApplicationStatus', isEqualTo: ResellerApplicationStatus.pending.value)
          .orderBy('resellerApplicationDate', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => UserModel.fromDocument(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get pending reseller applications: ${e.toString()}');
    }
  }

  // Reset password for user
  Future<void> resetUserPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
    } catch (e) {
      throw Exception('Failed to send password reset email: ${e.toString()}');
    }
  }
}
