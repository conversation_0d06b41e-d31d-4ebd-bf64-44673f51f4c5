org.gradle.jvmargs=-Xmx4G -XX:MaxMetaspaceSize=2G -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true

# Suppress compileSdk warnings
android.suppressUnsupportedCompileSdk=35

# Enable R8 full mode
android.enableR8.fullMode=true

# Use newer Gradle features
org.gradle.parallel=true
org.gradle.caching=true

# Kotlin daemon configuration
kotlin.daemon.jvmargs=-Xmx2G -XX:MaxMetaspaceSize=512M
kotlin.incremental=true
kotlin.incremental.android=true
