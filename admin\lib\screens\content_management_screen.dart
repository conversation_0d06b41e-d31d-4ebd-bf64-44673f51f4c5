import 'package:flutter/material.dart';
import '../constants/app_constants.dart';

class ContentManagementScreen extends StatefulWidget {
  const ContentManagementScreen({super.key});

  @override
  State<ContentManagementScreen> createState() => _ContentManagementScreenState();
}

class _ContentManagementScreenState extends State<ContentManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Content Management'),
        automaticallyImplyLeading: false,
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.post_add),
              text: 'Posts',
            ),
            Tab(
              icon: Icon(Icons.shopping_bag),
              text: 'Products',
            ),
            Tab(
              icon: Icon(Icons.comment),
              text: 'Comments',
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: const [
          PostsManagementTab(),
          ProductsManagementTab(),
          CommentsManagementTab(),
        ],
      ),
    );
  }
}

class PostsManagementTab extends StatefulWidget {
  const PostsManagementTab({super.key});

  @override
  State<PostsManagementTab> createState() => _PostsManagementTabState();
}

class _PostsManagementTabState extends State<PostsManagementTab> {
  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.post_add,
            size: 64,
            color: AppConstants.textHintColor,
          ),
          SizedBox(height: AppConstants.paddingMedium),
          Text(
            'Posts Management',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Manage user posts, moderate content, and handle reports',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: AppConstants.textHintColor,
            ),
          ),
          SizedBox(height: AppConstants.paddingLarge),
          Text(
            'Coming Soon...',
            style: TextStyle(
              color: AppConstants.primaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

class ProductsManagementTab extends StatefulWidget {
  const ProductsManagementTab({super.key});

  @override
  State<ProductsManagementTab> createState() => _ProductsManagementTabState();
}

class _ProductsManagementTabState extends State<ProductsManagementTab> {
  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.shopping_bag,
            size: 64,
            color: AppConstants.textHintColor,
          ),
          SizedBox(height: AppConstants.paddingMedium),
          Text(
            'Products Management',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Manage products, approve listings, and handle featured products',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: AppConstants.textHintColor,
            ),
          ),
          SizedBox(height: AppConstants.paddingLarge),
          Text(
            'Coming Soon...',
            style: TextStyle(
              color: AppConstants.primaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

class CommentsManagementTab extends StatefulWidget {
  const CommentsManagementTab({super.key});

  @override
  State<CommentsManagementTab> createState() => _CommentsManagementTabState();
}

class _CommentsManagementTabState extends State<CommentsManagementTab> {
  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.comment,
            size: 64,
            color: AppConstants.textHintColor,
          ),
          SizedBox(height: AppConstants.paddingMedium),
          Text(
            'Comments Management',
            style: TextStyle(
              fontSize: AppConstants.fontSizeXLarge,
              fontWeight: FontWeight.bold,
              color: AppConstants.textSecondaryColor,
            ),
          ),
          SizedBox(height: AppConstants.paddingSmall),
          Text(
            'Moderate comments, handle reports, and manage discussions',
            textAlign: TextAlign.center,
            style: TextStyle(
              color: AppConstants.textHintColor,
            ),
          ),
          SizedBox(height: AppConstants.paddingLarge),
          Text(
            'Coming Soon...',
            style: TextStyle(
              color: AppConstants.primaryColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
