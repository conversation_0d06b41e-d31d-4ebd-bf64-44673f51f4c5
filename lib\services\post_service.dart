import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:uuid/uuid.dart';
import 'package:image_picker/image_picker.dart';
import '../models/post_model.dart';
import '../models/user_model.dart';
import '../constants/app_constants.dart';
import 'image_service.dart';
import 'cloudinary_service.dart';

class PostService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = AppConstants.postsCollection;
  static const Uuid _uuid = Uuid();

  /// Create a new post
  static Future<PostModel?> createPost({
    required UserModel user,
    required String content,
    List<XFile>? imageFiles,
  }) async {
    try {
      print('PostService: Creating post...');

      // Generate unique post ID
      final postId = _uuid.v4();
      final now = DateTime.now();

      print('PostService: Generated post ID: $postId');
      print('PostService: Content length: ${content.length}');
      print('PostService: Number of image files: ${imageFiles?.length ?? 0}');

      // Upload images to Cloudinary if provided
      List<String> imageUrls = [];
      if (imageFiles != null && imageFiles.isNotEmpty) {
        print('PostService: Starting image upload process...');
        imageUrls = await ImageService.uploadPostImages(
          imageFiles: imageFiles,
          postId: postId,
        );
        print('PostService: Image upload completed. ${imageUrls.length}/${imageFiles.length} images uploaded successfully');

        if (imageUrls.length != imageFiles.length) {
          print('PostService: Warning - Not all images were uploaded successfully');
        }
      }

      // Create post model
      final post = PostModel(
        id: postId,
        userId: user.id,
        username: user.username,
        userDisplayName: user.displayName,
        userProfileImageUrl: user.profileImageUrl,
        content: content,
        imageUrls: imageUrls,
        createdAt: now,
        updatedAt: now,
      );

      // Save to Firestore
      await _firestore
          .collection(_collection)
          .doc(postId)
          .set(post.toMap());

      return post;
    } catch (e) {
      print('Error creating post: $e');
      return null;
    }
  }

  // Get all posts
  static Future<List<PostModel>> getAllPosts() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => PostModel.fromMap(doc.data()))
          .toList();
    } catch (e) {
      print('Error fetching posts: $e');
      return [];
    }
  }

  // Get posts by user ID
  static Future<List<PostModel>> getPostsByUserId(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('userId', isEqualTo: userId)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => PostModel.fromMap(doc.data()))
          .toList();
    } catch (e) {
      print('Error fetching user posts: $e');
      return [];
    }
  }

  // Get post by ID
  static Future<PostModel?> getPostById(String postId) async {
    try {
      final docSnapshot = await _firestore
          .collection(_collection)
          .doc(postId)
          .get();

      if (docSnapshot.exists) {
        return PostModel.fromMap(docSnapshot.data()!);
      }
      return null;
    } catch (e) {
      print('Error fetching post by ID: $e');
      return null;
    }
  }

  // Add new post
  static Future<bool> addPost(PostModel post) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(post.id)
          .set(post.toMap());
      return true;
    } catch (e) {
      print('Error adding post: $e');
      return false;
    }
  }

  // Update post
  static Future<bool> updatePost(PostModel post) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(post.id)
          .update(post.toMap());
      return true;
    } catch (e) {
      print('Error updating post: $e');
      return false;
    }
  }

  // Delete post with images (soft delete + image cleanup)
  static Future<bool> deletePost(String postId) async {
    try {
      print('PostService: Starting post deletion for ID: $postId');

      // First, get the post to check for images
      final postDoc = await _firestore
          .collection(_collection)
          .doc(postId)
          .get();

      if (!postDoc.exists) {
        print('PostService: Post not found: $postId');
        return false;
      }

      final postData = postDoc.data()!;
      final imageUrls = List<String>.from(postData['imageUrls'] ?? []);

      print('PostService: Post has ${imageUrls.length} images to delete');

      // Delete images from Cloudinary if any exist
      if (imageUrls.isNotEmpty) {
        print('PostService: Deleting images from Cloudinary...');
        final deletedPublicIds = await CloudinaryService.deleteMultipleImages(imageUrls);
        print('PostService: ${deletedPublicIds.length}/${imageUrls.length} images deleted from Cloudinary');
      }

      // Soft delete the post in Firestore
      await _firestore
          .collection(_collection)
          .doc(postId)
          .update({
        'isActive': false,
        'updatedAt': Timestamp.now(),
      });

      print('PostService: Post successfully deleted: $postId');
      return true;
    } catch (e) {
      print('PostService: Error deleting post: $e');
      return false;
    }
  }

  // Delete post (legacy method - kept for backward compatibility)
  static Future<bool> deletePostOnly(String postId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(postId)
          .update({
        'isActive': false,
        'updatedAt': Timestamp.now(),
      });
      return true;
    } catch (e) {
      print('Error deleting post: $e');
      return false;
    }
  }

  // Like/Unlike post
  static Future<bool> toggleLike(String postId, String userId) async {
    try {
      final postRef = _firestore.collection(_collection).doc(postId);

      return await _firestore.runTransaction((transaction) async {
        final postSnapshot = await transaction.get(postRef);

        if (!postSnapshot.exists) {
          throw Exception('Post does not exist');
        }

        final post = PostModel.fromMap(postSnapshot.data()!);
        List<String> likes = List.from(post.likes);

        if (likes.contains(userId)) {
          likes.remove(userId);
        } else {
          likes.add(userId);
        }

        transaction.update(postRef, {
          'likes': likes,
          'updatedAt': Timestamp.now(),
        });

        return true;
      });
    } catch (e) {
      print('Error toggling like: $e');
      return false;
    }
  }

  /// Add comment to post
  static Future<bool> addComment({
    required String postId,
    required String commentId,
  }) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(postId)
          .update({
        'comments': FieldValue.arrayUnion([commentId]),
        'updatedAt': Timestamp.now(),
      });

      return true;
    } catch (e) {
      print('Error adding comment: $e');
      return false;
    }
  }

  /// Remove comment from post
  static Future<bool> removeComment({
    required String postId,
    required String commentId,
  }) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(postId)
          .update({
        'comments': FieldValue.arrayRemove([commentId]),
        'updatedAt': Timestamp.now(),
      });

      return true;
    } catch (e) {
      print('Error removing comment: $e');
      return false;
    }
  }

  // Test Firestore connection
  static Future<bool> testFirestoreConnection() async {
    try {
      print('PostService: Testing Firestore connection...');
      final testDoc = await _firestore.collection('test').doc('connection').get();
      print('PostService: Firestore connection test successful');
      return true;
    } catch (e) {
      print('PostService: Firestore connection test failed: $e');
      return false;
    }
  }

  // Get posts stream for real-time updates
  static Stream<List<PostModel>> getPostsStream() {
    try {
      print('PostService: Starting posts stream...');
      print('PostService: Collection: $_collection');

      return _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) {
            print('PostService: Received ${snapshot.docs.length} posts');
            return snapshot.docs
                .map((doc) {
                  try {
                    final data = doc.data();
                    print('PostService: Processing post ${doc.id} with data keys: ${data.keys.toList()}');
                    return PostModel.fromMap(data);
                  } catch (e) {
                    print('PostService: Error parsing post ${doc.id}: $e');
                    return null;
                  }
                })
                .where((post) => post != null)
                .cast<PostModel>()
                .toList();
          })
          .handleError((error) {
            print('PostService: Stream error: $error');
            throw error;
          });
    } catch (e) {
      print('PostService: Error creating posts stream: $e');
      throw e;
    }
  }

  // Get user posts stream (My Posts filter)
  static Stream<List<PostModel>> getUserPostsStream(String userId) {
    try {
      print('PostService: Getting user posts stream for userId: $userId');
      return _firestore
          .collection(_collection)
          .where('userId', isEqualTo: userId)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) {
            print('PostService: Received ${snapshot.docs.length} user posts');
            return snapshot.docs
                .map((doc) => PostModel.fromMap(doc.data()))
                .toList();
          });
    } catch (e) {
      print('PostService: Error creating user posts stream: $e');
      throw e;
    }
  }

  // Get followed users posts stream (Followed filter)
  static Stream<List<PostModel>> getFollowedPostsStream(String currentUserId) {
    try {
      print('PostService: Getting followed posts stream for userId: $currentUserId');
      // For now, return all posts except current user's posts
      // In a real app, you would first get the list of followed user IDs
      // then filter posts by those user IDs
      return _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .where('userId', isNotEqualTo: currentUserId)
          .orderBy('userId') // Required when using isNotEqualTo
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) {
            print('PostService: Received ${snapshot.docs.length} followed posts');
            return snapshot.docs
                .map((doc) => PostModel.fromMap(doc.data()))
                .toList();
          });
    } catch (e) {
      print('PostService: Error creating followed posts stream: $e');
      // Fallback to global posts if error
      return getPostsStream();
    }
  }

  // Get favorite posts stream (Favorites filter)
  static Stream<List<PostModel>> getFavoritePostsStream(String currentUserId) {
    try {
      print('PostService: Getting favorite posts stream for userId: $currentUserId');
      // For now, return posts that have more than 0 likes as "favorites"
      // In a real app, you would maintain a separate favorites collection
      return _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map((snapshot) {
            print('PostService: Received ${snapshot.docs.length} posts for favorites filtering');
            final favoritePosts = snapshot.docs
                .map((doc) => PostModel.fromMap(doc.data()))
                .where((post) => post.likeCount > 0) // Simple favorite logic
                .toList();
            print('PostService: Filtered to ${favoritePosts.length} favorite posts');
            return favoritePosts;
          });
    } catch (e) {
      print('PostService: Error creating favorite posts stream: $e');
      // Fallback to global posts if error
      return getPostsStream();
    }
  }

  // Get filtered posts stream based on filter type
  static Stream<List<PostModel>> getFilteredPostsStream(String filterType, String? currentUserId) {
    switch (filterType) {
      case 'My Posts':
        if (currentUserId != null) {
          return getUserPostsStream(currentUserId);
        }
        return getPostsStream();
      case 'Followed':
        if (currentUserId != null) {
          return getFollowedPostsStream(currentUserId);
        }
        return getPostsStream();
      case 'Favorites':
        if (currentUserId != null) {
          return getFavoritePostsStream(currentUserId);
        }
        return getPostsStream();
      case 'Global':
      default:
        return getPostsStream();
    }
  }

  // Search posts
  static Future<List<PostModel>> searchPosts(String query) async {
    try {
      if (query.isEmpty) return getAllPosts();

      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .get();

      final allPosts = querySnapshot.docs
          .map((doc) => PostModel.fromMap(doc.data()))
          .toList();

      // Filter posts based on search query
      return allPosts.where((post) {
        final searchQuery = query.toLowerCase();
        return post.content.toLowerCase().contains(searchQuery) ||
               post.userDisplayName.toLowerCase().contains(searchQuery) ||
               post.username.toLowerCase().contains(searchQuery);
      }).toList();
    } catch (e) {
      print('Error searching posts: $e');
      return [];
    }
  }

  // Get trending posts (posts with most likes in last 24 hours)
  static Future<List<PostModel>> getTrendingPosts() async {
    try {
      final yesterday = DateTime.now().subtract(const Duration(days: 1));
      
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .where('createdAt', isGreaterThan: Timestamp.fromDate(yesterday))
          .get();

      final posts = querySnapshot.docs
          .map((doc) => PostModel.fromMap(doc.data()))
          .toList();

      // Sort by like count
      posts.sort((a, b) => b.likeCount.compareTo(a.likeCount));
      
      return posts.take(10).toList();
    } catch (e) {
      print('Error fetching trending posts: $e');
      return [];
    }
  }

  // Get posts with pagination
  static Future<List<PostModel>> getPostsPaginated({
    String filter = 'Global',
    String? currentUserId,
    DocumentSnapshot? lastDocument,
    int limit = 10,
  }) async {
    try {
      Query query = _firestore
          .collection(_collection)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true);

      // Apply filter
      if (filter == 'Followed' && currentUserId != null) {
        // Get user's following list first
        try {
          final userDoc = await _firestore
              .collection('users')
              .doc(currentUserId)
              .get();

          if (userDoc.exists) {
            final userData = userDoc.data() as Map<String, dynamic>;
            final following = List<String>.from(userData['following'] ?? []);

            if (following.isNotEmpty) {
              // Firestore whereIn has a limit of 10 items
              if (following.length <= 10) {
                query = query.where('userId', whereIn: following);
              } else {
                // If following more than 10 users, use a different approach
                // For now, just show all posts (can be optimized later)
                print('Following more than 10 users, showing all posts');
              }
            } else {
              // If not following anyone, return empty list
              return [];
            }
          } else {
            // User document doesn't exist, return empty list
            return [];
          }
        } catch (e) {
          print('Error getting following list: $e');
          // Fallback to showing empty list for followed filter
          return [];
        }
      } else if (filter == 'My Posts' && currentUserId != null) {
        query = query.where('userId', isEqualTo: currentUserId);
      } else if (filter == 'Favorites' && currentUserId != null) {
        // For favorites, we'll filter after getting the posts
        // since we need to check like counts
      }

      // Apply pagination
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      query = query.limit(limit);

      final querySnapshot = await query.get();

      // Fetch user data for each post to get updated profile pictures
      final List<PostModel> posts = [];
      final Map<String, Map<String, dynamic>> userCache = {};

      // Get unique user IDs
      final userIds = querySnapshot.docs
          .map((doc) => (doc.data() as Map<String, dynamic>)['userId'] as String)
          .toSet()
          .toList();

      // Batch fetch user data
      for (final userId in userIds) {
        try {
          final userDoc = await _firestore
              .collection('users')
              .doc(userId)
              .get();

          if (userDoc.exists) {
            userCache[userId] = userDoc.data() as Map<String, dynamic>;
          }
        } catch (e) {
          print('Error fetching user data for $userId: $e');
        }
      }

      // Process posts with cached user data
      for (final doc in querySnapshot.docs) {
        final postData = doc.data() as Map<String, dynamic>;
        final userId = postData['userId'] as String;

        // Update post data with cached user info
        if (userCache.containsKey(userId)) {
          final userData = userCache[userId]!;
          postData['userDisplayName'] = userData['displayName'] ?? postData['userDisplayName'];
          postData['userProfileImageUrl'] = userData['profileImageUrl'];
          postData['username'] = userData['username'] ?? postData['username'];
        }

        final post = PostModel(
          id: postData['id'] ?? '',
          userId: postData['userId'] ?? '',
          username: postData['username'] ?? '',
          userDisplayName: postData['userDisplayName'] ?? '',
          userProfileImageUrl: postData['userProfileImageUrl'],
          content: postData['content'] ?? '',
          imageUrls: List<String>.from(postData['imageUrls'] ?? []),
          createdAt: (postData['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
          updatedAt: (postData['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
          likes: List<String>.from(postData['likes'] ?? []),
          comments: List<String>.from(postData['comments'] ?? []),
          shares: List<String>.from(postData['shares'] ?? []),
          isActive: postData['isActive'] ?? true,
          lastDocument: doc,
        );

        posts.add(post);
      }

      // Apply favorites filter if needed
      if (filter == 'Favorites' && currentUserId != null) {
        return posts.where((post) => post.likeCount > 0).toList();
      }

      return posts;
    } catch (e) {
      print('Error fetching posts with pagination: $e');
      return [];
    }
  }

  // Refresh posts (clear cache and reload)
  static Future<List<PostModel>> refreshPosts({
    String filter = 'Global',
    String? currentUserId,
    int limit = 10,
  }) async {
    return getPostsPaginated(
      filter: filter,
      currentUserId: currentUserId,
      lastDocument: null,
      limit: limit,
    );
  }
}
