import 'package:flutter/material.dart';
import '../../constants/app_constants.dart';
import '../../models/user_model.dart';

class BioSection extends StatelessWidget {
  final UserModel? user;

  const BioSection({
    super.key,
    this.user,
  });

  @override
  Widget build(BuildContext context) {
    if (user?.bio == null || user!.bio!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppConstants.backgroundColor.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppConstants.primaryColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildBioHeader(context),
          const SizedBox(height: 8),
          _buildBioText(context),
          if (user?.address != null || user?.country != null) ...[
            const SizedBox(height: 12),
            _buildLocationInfo(context),
          ],
        ],
      ),
    );
  }

  Widget _buildBioHeader(BuildContext context) {
    return Row(
      children: [
        Icon(
          Icons.info_outline,
          size: 18,
          color: AppConstants.primaryColor,
        ),
        const SizedBox(width: 8),
        Text(
          'About',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppConstants.primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildBioText(BuildContext context) {
    return Text(
      user!.bio!,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        color: AppConstants.textSecondaryColor,
        height: 1.4,
      ),
    );
  }

  Widget _buildLocationInfo(BuildContext context) {
    final locationParts = <String>[];
    
    if (user?.address != null && user!.address!.isNotEmpty) {
      locationParts.add(user!.address!);
    }
    
    if (user?.country != null && user!.country!.isNotEmpty) {
      locationParts.add(user!.country!);
    }

    if (locationParts.isEmpty) return const SizedBox.shrink();

    return Row(
      children: [
        Icon(
          Icons.location_on_outlined,
          size: 16,
          color: AppConstants.textSecondaryColor,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            locationParts.join(', '),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppConstants.textSecondaryColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }
}
