import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:image_picker/image_picker.dart';
import '../constants/app_constants.dart';
import '../models/product_model.dart';
import '../services/product_service.dart';
import '../services/image_service.dart';
import '../providers/auth_provider.dart';
import '../widgets/product_dashboard/product_form_widgets.dart';
import '../utils/role_access_control.dart';

class AddProductScreen extends StatefulWidget {
  const AddProductScreen({super.key});

  @override
  State<AddProductScreen> createState() => _AddProductScreenState();
}

class _AddProductScreenState extends State<AddProductScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _priceController = TextEditingController();
  final _originalPriceController = TextEditingController();
  final _stockController = TextEditingController();
  final _whatsAppController = TextEditingController();
  final _weChatController = TextEditingController();


  String _selectedCategory = 'Electronics';
  List<XFile> _selectedImages = [];
  List<String> _tags = [];
  bool _isAvailable = true;
  bool _isFeatured = false;
  bool _isLoading = false;

  final List<String> _categories = [
    'Electronics',
    'Fashion',
    'Furniture',
    'Sports',
    'Books',
    'Beauty',
    'Automotive',
    'Food & Beverages',
    'Health & Wellness',
    'Home & Garden',
    'Toys & Games',
    'Other',
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _originalPriceController.dispose();
    _stockController.dispose();
    _whatsAppController.dispose();
    _weChatController.dispose();

    super.dispose();
  }

  Future<void> _pickImages() async {
    try {
      final images = await ImageService.pickMultipleImages(maxImages: 5);

      if (images != null && images.isNotEmpty) {
        setState(() {
          _selectedImages.addAll(images);
          if (_selectedImages.length > 5) {
            _selectedImages = _selectedImages.take(5).toList();
          }
        });

        // Show success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('${images.length} image(s) selected successfully'),
              backgroundColor: AppConstants.successColor,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      _showErrorSnackBar('Error selecting images: $e');
    }
  }

  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
  }

  void _addTag(String tag) {
    if (tag.isNotEmpty && !_tags.contains(tag.toLowerCase())) {
      setState(() {
        _tags.add(tag.toLowerCase());
      });
    }
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
  }

  Future<void> _saveProduct() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedImages.isEmpty) {
      _showErrorSnackBar('Please add at least one product image');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final user = authProvider.currentUser;

      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Generate product ID
      final productId = DateTime.now().millisecondsSinceEpoch.toString();

      // Upload images
      final imageUrls = await ImageService.uploadProductImages(
        imageFiles: _selectedImages,
        productId: productId,
      );

      if (imageUrls.isEmpty) {
        throw Exception('Failed to upload images');
      }

      // Create product model
      final product = ProductModel(
        id: productId,
        sellerId: user.id,
        sellerName: user.displayName,
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
        price: double.parse(_priceController.text),
        originalPrice: _originalPriceController.text.isNotEmpty
            ? double.parse(_originalPriceController.text)
            : null,
        category: _selectedCategory,
        imageUrls: imageUrls,
        tags: _tags,
        stockQuantity: _stockController.text.trim().isNotEmpty
            ? int.parse(_stockController.text)
            : 999999, // Large number for unlimited stock
        isAvailable: _isAvailable,
        isFeatured: _isFeatured,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        sellerWhatsApp: _whatsAppController.text.trim().isNotEmpty
            ? _whatsAppController.text.trim()
            : null,
        sellerWeChat: _weChatController.text.trim().isNotEmpty
            ? _weChatController.text.trim()
            : null,
        sellerLocation: null,
      );

      // Save to Firestore
      final success = await ProductService.addProduct(product);

      if (success) {
        if (mounted) {
          Navigator.of(context).pop(product);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Product added successfully!'),
              backgroundColor: AppConstants.successColor,
            ),
          );
        }
      } else {
        throw Exception('Failed to save product');
      }
    } catch (e) {
      _showErrorSnackBar('Error saving product: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppConstants.errorColor,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = Provider.of<AuthProvider>(context);
    final currentUser = authProvider.currentUser;

    // Check if user can upload products
    if (!RoleAccessControl.canUploadProducts(currentUser)) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Access Denied'),
          backgroundColor: AppConstants.surfaceColor,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: AppConstants.textPrimaryColor),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(
                  Icons.lock_outline,
                  size: 64,
                  color: AppConstants.textSecondaryColor,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Upload Restricted',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w600,
                    color: AppConstants.textPrimaryColor,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  RoleAccessControl.getUnauthorizedMessage('upload_product'),
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    fontSize: 16,
                    color: AppConstants.textSecondaryColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Scaffold(
      backgroundColor: AppConstants.backgroundColor,
      appBar: AppBar(
        title: const Text(
          'Add Product',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: AppConstants.textPrimaryColor,
          ),
        ),
        backgroundColor: AppConstants.surfaceColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppConstants.textPrimaryColor),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveProduct,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: AppConstants.primaryColor,
                    ),
                  )
                : const Text(
                    'Save',
                    style: TextStyle(
                      color: AppConstants.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.fromLTRB(
            AppConstants.paddingMedium,
            AppConstants.paddingMedium,
            AppConstants.paddingMedium,
            100, // Bottom padding for navigation gap
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header Card
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(AppConstants.paddingLarge),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppConstants.primaryColor,
                      AppConstants.primaryColor.withOpacity(0.8),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
                  boxShadow: [
                    BoxShadow(
                      color: AppConstants.primaryColor.withOpacity(0.3),
                      blurRadius: 12,
                      offset: const Offset(0, 6),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.add_business,
                      size: 48,
                      color: Colors.white,
                    ),
                    const SizedBox(height: AppConstants.paddingMedium),
                    const Text(
                      'Add New Product',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: AppConstants.paddingSmall),
                    const Text(
                      'Fill in the details below to list your product',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.white70,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppConstants.paddingLarge),
              // Product Images Section
              _buildSectionCard(
                title: 'Product Images',
                subtitle: 'Add up to 5 high-quality images',
                icon: Icons.photo_library,
                child: ProductImagePicker(
                  images: _selectedImages,
                  onPickImages: _pickImages,
                  onRemoveImage: _removeImage,
                ),
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Basic Information
              _buildSectionCard(
                title: 'Basic Information',
                subtitle: 'Tell us about your product',
                icon: Icons.info_outline,
                child: Column(
                  children: [
                    ProductFormField(
                      controller: _nameController,
                      label: 'Product Name',
                      hint: 'Enter product name',
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Product name is required';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: AppConstants.paddingMedium),

                    ProductFormField(
                      controller: _descriptionController,
                      label: 'Description',
                      hint: 'Describe your product in detail',
                      maxLines: 4,
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return 'Description is required';
                        }
                        return null;
                      },
                    ),

                    const SizedBox(height: AppConstants.paddingMedium),

                    CategoryDropdown(
                      value: _selectedCategory,
                      categories: _categories,
                      onChanged: (value) {
                        setState(() {
                          _selectedCategory = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Pricing Section
              _buildSectionCard(
                title: 'Pricing & Stock',
                subtitle: 'Set your product pricing',
                icon: Icons.attach_money,
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: ProductFormField(
                            controller: _priceController,
                            label: 'Price (\$)',
                            hint: '0.00',
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value == null || value.trim().isEmpty) {
                                return 'Price is required';
                              }
                              if (double.tryParse(value) == null) {
                                return 'Enter valid price';
                              }
                              return null;
                            },
                          ),
                        ),
                        const SizedBox(width: AppConstants.paddingMedium),
                        Expanded(
                          child: ProductFormField(
                            controller: _originalPriceController,
                            label: 'Original Price (\$)',
                            hint: '0.00 (optional)',
                            keyboardType: TextInputType.number,
                            validator: (value) {
                              if (value != null && value.trim().isNotEmpty) {
                                if (double.tryParse(value) == null) {
                                  return 'Enter valid price';
                                }
                              }
                              return null;
                            },
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: AppConstants.paddingMedium),

                    ProductFormField(
                      controller: _stockController,
                      label: 'Stock Quantity (Optional)',
                      hint: 'Enter stock quantity (leave empty for unlimited)',
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value != null && value.trim().isNotEmpty) {
                          if (int.tryParse(value) == null) {
                            return 'Enter valid quantity';
                          }
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Tags Section
              _buildSectionCard(
                title: 'Tags',
                subtitle: 'Add keywords to help buyers find your product',
                icon: Icons.local_offer,
                child: TagInput(
                  tags: _tags,
                  onAddTag: _addTag,
                  onRemoveTag: _removeTag,
                ),
              ),

              const SizedBox(height: AppConstants.paddingLarge),

              // Contact Information
              _buildSectionCard(
                title: 'Contact Information',
                subtitle: 'Optional: Add contact details for direct communication',
                icon: Icons.contact_phone,
                child: Column(
                  children: [
                    ProductFormField(
                      controller: _whatsAppController,
                      label: 'WhatsApp Number',
                      hint: '+1234567890',
                      keyboardType: TextInputType.phone,
                    ),

                    const SizedBox(height: AppConstants.paddingMedium),

                    ProductFormField(
                      controller: _weChatController,
                      label: 'WeChat ID',
                      hint: 'Your WeChat ID',
                    ),
                  ],
                ),
              ),



              const SizedBox(height: AppConstants.paddingLarge),

              // Settings Section
              _buildSectionCard(
                title: 'Product Settings',
                subtitle: 'Configure product visibility and features',
                icon: Icons.settings,
                child: Column(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: AppConstants.backgroundColor,
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                      ),
                      child: SwitchListTile(
                        title: const Text(
                          'Available for Sale',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        subtitle: const Text('Make this product visible to buyers'),
                        value: _isAvailable,
                        onChanged: (value) {
                          setState(() {
                            _isAvailable = value;
                          });
                        },
                        activeColor: AppConstants.primaryColor,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: AppConstants.paddingMedium,
                          vertical: AppConstants.paddingSmall,
                        ),
                      ),
                    ),

                    const SizedBox(height: AppConstants.paddingMedium),

                    Container(
                      decoration: BoxDecoration(
                        color: AppConstants.backgroundColor,
                        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
                      ),
                      child: SwitchListTile(
                        title: const Text(
                          'Featured Product',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        subtitle: const Text('Highlight this product in search results'),
                        value: _isFeatured,
                        onChanged: (value) {
                          setState(() {
                            _isFeatured = value;
                          });
                        },
                        activeColor: AppConstants.primaryColor,
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: AppConstants.paddingMedium,
                          vertical: AppConstants.paddingSmall,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: AppConstants.paddingXLarge),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required String subtitle,
    required IconData icon,
    required Widget child,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: AppConstants.surfaceColor,
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.paddingLarge),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppConstants.primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: AppConstants.primaryColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeLarge,
                          fontWeight: FontWeight.w600,
                          color: AppConstants.textPrimaryColor,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        subtitle,
                        style: const TextStyle(
                          fontSize: AppConstants.fontSizeSmall,
                          color: AppConstants.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConstants.paddingLarge),
            child,
          ],
        ),
      ),
    );
  }
}
