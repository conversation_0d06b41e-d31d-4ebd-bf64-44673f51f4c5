import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import '../models/post_model.dart';
import '../models/product_model.dart';
import '../constants/app_constants.dart';
import '../enums/user_role.dart';

class UserManagementService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get all users with filters and pagination
  static Future<List<UserModel>> getUsers({
    int limit = 20,
    DocumentSnapshot? lastDocument,
    UserRole? roleFilter,
    bool? isActiveFilter,
    bool? isVerifiedFilter,
    String? searchQuery,
  }) async {
    try {
      Query query = _firestore
          .collection(AppConstants.usersCollection)
          .orderBy('createdAt', descending: true);

      // Apply filters
      if (roleFilter != null) {
        query = query.where('role', isEqualTo: roleFilter.value);
      }
      if (isActiveFilter != null) {
        query = query.where('isActive', isEqualTo: isActiveFilter);
      }
      if (isVerifiedFilter != null) {
        query = query.where('isVerified', isEqualTo: isVerifiedFilter);
      }

      // Apply pagination
      if (lastDocument != null) {
        query = query.startAfterDocument(lastDocument);
      }

      query = query.limit(limit);

      final QuerySnapshot querySnapshot = await query.get();
      List<UserModel> users = querySnapshot.docs
          .map((doc) => UserModel.fromDocument(doc))
          .toList();

      // Apply search filter locally if provided
      if (searchQuery != null && searchQuery.isNotEmpty) {
        final searchLower = searchQuery.toLowerCase();
        users = users.where((user) {
          return user.username.toLowerCase().contains(searchLower) ||
                 user.displayName.toLowerCase().contains(searchLower) ||
                 user.email.toLowerCase().contains(searchLower);
        }).toList();
      }

      return users;
    } catch (e) {
      throw Exception('Failed to get users: ${e.toString()}');
    }
  }

  // Get user by ID with detailed information
  static Future<UserModel?> getUserById(String userId) async {
    try {
      final DocumentSnapshot doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (doc.exists) {
        return UserModel.fromDocument(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get user: ${e.toString()}');
    }
  }

  // Get user's posts
  static Future<List<PostModel>> getUserPosts(String userId, {int limit = 10}) async {
    try {
      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppConstants.postsCollection)
          .where('userId', isEqualTo: userId)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      return querySnapshot.docs
          .map((doc) => PostModel.fromDocument(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get user posts: ${e.toString()}');
    }
  }

  // Get user's products (for resellers)
  static Future<List<ProductModel>> getUserProducts(String userId, {int limit = 10}) async {
    try {
      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppConstants.productsCollection)
          .where('sellerId', isEqualTo: userId)
          .where('isAvailable', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      return querySnapshot.docs
          .map((doc) => ProductModel.fromDocument(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get user products: ${e.toString()}');
    }
  }

  // Update user role
  static Future<void> updateUserRole(String userId, UserRole newRole) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'role': newRole.value,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to update user role: ${e.toString()}');
    }
  }

  // Toggle user active status
  static Future<void> toggleUserActiveStatus(String userId, bool isActive) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'isActive': isActive,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to update user status: ${e.toString()}');
    }
  }

  // Toggle user verified status
  static Future<void> toggleUserVerifiedStatus(String userId, bool isVerified) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'isVerified': isVerified,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to update user verification: ${e.toString()}');
    }
  }

  // Approve reseller application
  static Future<void> approveResellerApplication(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'role': UserRole.reseller.value,
        'resellerApplicationStatus': ResellerApplicationStatus.approved.value,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to approve reseller application: ${e.toString()}');
    }
  }

  // Reject reseller application
  static Future<void> rejectResellerApplication(String userId, String reason) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'resellerApplicationStatus': ResellerApplicationStatus.rejected.value,
        'resellerApplicationReason': reason,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to reject reseller application: ${e.toString()}');
    }
  }

  // Get pending reseller applications
  static Future<List<UserModel>> getPendingResellerApplications() async {
    try {
      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('resellerApplicationStatus', isEqualTo: ResellerApplicationStatus.pending.value)
          .orderBy('resellerApplicationDate', descending: false)
          .get();

      return querySnapshot.docs
          .map((doc) => UserModel.fromDocument(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get pending applications: ${e.toString()}');
    }
  }

  // Get users by role
  static Future<List<UserModel>> getUsersByRole(UserRole role, {int limit = 50}) async {
    try {
      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('role', isEqualTo: role.value)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .limit(limit)
          .get();

      return querySnapshot.docs
          .map((doc) => UserModel.fromDocument(doc))
          .toList();
    } catch (e) {
      throw Exception('Failed to get users by role: ${e.toString()}');
    }
  }

  // Search users
  static Future<List<UserModel>> searchUsers(String searchQuery, {int limit = 20}) async {
    try {
      // Get all users and filter locally for better search
      final QuerySnapshot querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .limit(limit * 3) // Get more to filter locally
          .get();

      final List<UserModel> allUsers = querySnapshot.docs
          .map((doc) => UserModel.fromDocument(doc))
          .toList();

      // Filter locally for better search
      final searchLower = searchQuery.toLowerCase();
      return allUsers.where((user) {
        return user.username.toLowerCase().contains(searchLower) ||
               user.displayName.toLowerCase().contains(searchLower) ||
               user.email.toLowerCase().contains(searchLower);
      }).take(limit).toList();
    } catch (e) {
      throw Exception('Failed to search users: ${e.toString()}');
    }
  }

  // Get user statistics
  static Future<Map<String, int>> getUserStatistics(String userId) async {
    try {
      final futures = await Future.wait([
        _getUserPostCount(userId),
        _getUserProductCount(userId),
        _getUserFollowerCount(userId),
        _getUserFollowingCount(userId),
      ]);

      return {
        'posts': futures[0],
        'products': futures[1],
        'followers': futures[2],
        'following': futures[3],
      };
    } catch (e) {
      throw Exception('Failed to get user statistics: ${e.toString()}');
    }
  }

  // Helper method to get user post count
  static Future<int> _getUserPostCount(String userId) async {
    final QuerySnapshot snapshot = await _firestore
        .collection(AppConstants.postsCollection)
        .where('userId', isEqualTo: userId)
        .where('isActive', isEqualTo: true)
        .get();
    return snapshot.docs.length;
  }

  // Helper method to get user product count
  static Future<int> _getUserProductCount(String userId) async {
    final QuerySnapshot snapshot = await _firestore
        .collection(AppConstants.productsCollection)
        .where('sellerId', isEqualTo: userId)
        .where('isAvailable', isEqualTo: true)
        .get();
    return snapshot.docs.length;
  }

  // Helper method to get user follower count
  static Future<int> _getUserFollowerCount(String userId) async {
    final DocumentSnapshot doc = await _firestore
        .collection(AppConstants.usersCollection)
        .doc(userId)
        .get();
    
    if (doc.exists) {
      final user = UserModel.fromDocument(doc);
      return user.followerCount;
    }
    return 0;
  }

  // Helper method to get user following count
  static Future<int> _getUserFollowingCount(String userId) async {
    final DocumentSnapshot doc = await _firestore
        .collection(AppConstants.usersCollection)
        .doc(userId)
        .get();
    
    if (doc.exists) {
      final user = UserModel.fromDocument(doc);
      return user.followingCount;
    }
    return 0;
  }

  // Delete user (soft delete by deactivating)
  static Future<void> deleteUser(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
        'isActive': false,
        'updatedAt': Timestamp.now(),
      });
    } catch (e) {
      throw Exception('Failed to delete user: ${e.toString()}');
    }
  }
}
