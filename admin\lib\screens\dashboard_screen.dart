import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../constants/app_constants.dart';
import '../providers/auth_provider.dart';
import '../models/analytics_model.dart';
import '../services/analytics_service.dart';
import '../widgets/dashboard/stats_card.dart';
import '../widgets/dashboard/analytics_chart.dart';
import '../widgets/dashboard/recent_activities.dart';
import 'user_management_screen.dart';
import 'content_management_screen.dart';
import 'analytics_screen.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  int _selectedIndex = 0;
  AnalyticsModel? _analytics;
  bool _isLoading = true;

  final List<Widget> _screens = [
    const DashboardHomeScreen(),
    const UserManagementScreen(),
    const ContentManagementScreen(),
    const AnalyticsScreen(),
  ];

  final List<NavigationRailDestination> _destinations = [
    const NavigationRailDestination(
      icon: Icon(Icons.dashboard_outlined),
      selectedIcon: Icon(Icons.dashboard),
      label: Text('Dashboard'),
    ),
    const NavigationRailDestination(
      icon: Icon(Icons.people_outlined),
      selectedIcon: Icon(Icons.people),
      label: Text('Users'),
    ),
    const NavigationRailDestination(
      icon: Icon(Icons.content_copy_outlined),
      selectedIcon: Icon(Icons.content_copy),
      label: Text('Content'),
    ),
    const NavigationRailDestination(
      icon: Icon(Icons.analytics_outlined),
      selectedIcon: Icon(Icons.analytics),
      label: Text('Analytics'),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _loadAnalytics();
  }

  Future<void> _loadAnalytics() async {
    try {
      final analytics = await AnalyticsService.getAnalytics();
      if (mounted) {
        setState(() {
          _analytics = analytics;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load analytics: $e'),
            backgroundColor: AppConstants.errorColor,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Row(
        children: [
          // Navigation Rail
          NavigationRail(
            selectedIndex: _selectedIndex,
            onDestinationSelected: (index) {
              setState(() {
                _selectedIndex = index;
              });
            },
            labelType: NavigationRailLabelType.all,
            backgroundColor: AppConstants.surfaceColor,
            destinations: _destinations,
            leading: _buildNavigationHeader(),
            trailing: _buildNavigationTrailing(),
          ),
          
          const VerticalDivider(thickness: 1, width: 1),
          
          // Main Content
          Expanded(
            child: _screens[_selectedIndex],
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationHeader() {
    return Column(
      children: [
        const SizedBox(height: AppConstants.paddingLarge),
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: AppConstants.primaryColor,
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
          ),
          child: const Icon(
            Icons.admin_panel_settings,
            color: Colors.white,
            size: 30,
          ),
        ),
        const SizedBox(height: AppConstants.paddingMedium),
        Text(
          'Admin',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConstants.paddingLarge),
      ],
    );
  }

  Widget _buildNavigationTrailing() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Column(
          children: [
            const SizedBox(height: AppConstants.paddingLarge),
            
            // User Info
            if (authProvider.currentUser != null) ...[
              CircleAvatar(
                radius: 20,
                backgroundColor: AppConstants.primaryColor.withOpacity(0.1),
                child: Text(
                  authProvider.currentUser!.displayName.isNotEmpty
                      ? authProvider.currentUser!.displayName[0].toUpperCase()
                      : authProvider.currentUser!.email[0].toUpperCase(),
                  style: const TextStyle(
                    color: AppConstants.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(height: AppConstants.paddingSmall),
              Text(
                authProvider.currentUser!.displayNameOrUsername,
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: AppConstants.paddingMedium),
            ],
            
            // Logout Button
            IconButton(
              onPressed: () => _handleLogout(authProvider),
              icon: const Icon(Icons.logout),
              tooltip: 'Logout',
            ),
            
            const SizedBox(height: AppConstants.paddingLarge),
          ],
        );
      },
    );
  }

  void _handleLogout(AuthProvider authProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Logout'),
        content: const Text('Are you sure you want to logout?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              authProvider.signOut();
            },
            child: const Text('Logout'),
          ),
        ],
      ),
    );
  }
}

class DashboardHomeScreen extends StatefulWidget {
  const DashboardHomeScreen({super.key});

  @override
  State<DashboardHomeScreen> createState() => _DashboardHomeScreenState();
}

class _DashboardHomeScreenState extends State<DashboardHomeScreen> {
  AnalyticsModel? _analytics;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadAnalytics();
  }

  Future<void> _loadAnalytics() async {
    try {
      final analytics = await AnalyticsService.getAnalytics();
      if (mounted) {
        setState(() {
          _analytics = analytics;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed: _loadAnalytics,
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh',
          ),
          const SizedBox(width: AppConstants.paddingMedium),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _analytics == null
              ? const Center(child: Text('Failed to load analytics'))
              : RefreshIndicator(
                  onRefresh: _loadAnalytics,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(AppConstants.paddingLarge),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Welcome Section
                        _buildWelcomeSection(),
                        
                        const SizedBox(height: AppConstants.paddingLarge),
                        
                        // Stats Cards
                        _buildStatsCards(),
                        
                        const SizedBox(height: AppConstants.paddingLarge),
                        
                        // Charts and Recent Activities
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Analytics Chart
                            Expanded(
                              flex: 2,
                              child: AnalyticsChart(analytics: _analytics!),
                            ),
                            
                            const SizedBox(width: AppConstants.paddingLarge),
                            
                            // Recent Activities
                            Expanded(
                              flex: 1,
                              child: RecentActivities(analytics: _analytics!),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
    );
  }

  Widget _buildWelcomeSection() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(AppConstants.paddingLarge),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: AppConstants.primaryColor,
                  child: Text(
                    authProvider.currentUser?.displayName.isNotEmpty == true
                        ? authProvider.currentUser!.displayName[0].toUpperCase()
                        : authProvider.currentUser?.email[0].toUpperCase() ?? 'A',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: AppConstants.paddingMedium),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Welcome back, ${authProvider.currentUser?.displayNameOrUsername ?? 'Admin'}!',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: AppConstants.paddingSmall),
                      Text(
                        'Here\'s what\'s happening with your platform today.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppConstants.textSecondaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatsCards() {
    return GridView.count(
      crossAxisCount: 4,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisSpacing: AppConstants.paddingMedium,
      mainAxisSpacing: AppConstants.paddingMedium,
      childAspectRatio: 1.5,
      children: [
        StatsCard(
          title: 'Total Users',
          value: _analytics!.totalUsers.toString(),
          icon: Icons.people,
          color: AppConstants.primaryColor,
          growth: _analytics!.userGrowthRate,
        ),
        StatsCard(
          title: 'Total Posts',
          value: _analytics!.totalPosts.toString(),
          icon: Icons.post_add,
          color: AppConstants.successColor,
          growth: _analytics!.postGrowthRate,
        ),
        StatsCard(
          title: 'Total Products',
          value: _analytics!.totalProducts.toString(),
          icon: Icons.shopping_bag,
          color: AppConstants.warningColor,
          growth: _analytics!.productGrowthRate,
        ),
        StatsCard(
          title: 'Pending Applications',
          value: _analytics!.pendingResellerApplications.toString(),
          icon: Icons.pending_actions,
          color: AppConstants.errorColor,
        ),
      ],
    );
  }
}
