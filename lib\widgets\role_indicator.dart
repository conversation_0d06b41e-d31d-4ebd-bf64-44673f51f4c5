import 'package:flutter/material.dart';
import '../models/user_model.dart';
import '../enums/user_role.dart';
import '../constants/app_constants.dart';
import '../utils/role_access_control.dart';

class RoleIndicator extends StatelessWidget {
  final UserModel user;
  final double size;
  final bool showLabel;

  const RoleIndicator({
    super.key,
    required this.user,
    this.size = 16.0,
    this.showLabel = false,
  });

  @override
  Widget build(BuildContext context) {
    final roleInfo = RoleAccessControl.getRoleDisplayInfo(user);
    
    if (!roleInfo['showIcon']) {
      return const SizedBox.shrink();
    }

    final iconData = _getIconData(roleInfo['iconData']);
    final color = Color(roleInfo['color']);

    if (showLabel) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              iconData,
              size: size,
              color: color,
            ),
            const SizedBox(width: 4),
            Text(
              roleInfo['label'],
              style: TextStyle(
                fontSize: size * 0.8,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
      child: Icon(
        iconData,
        size: size,
        color: Colors.white,
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'admin_panel_settings':
        return Icons.admin_panel_settings;
      case 'store':
        return Icons.store;
      default:
        return Icons.person;
    }
  }
}

class UserNameWithRole extends StatelessWidget {
  final UserModel user;
  final TextStyle? textStyle;
  final double iconSize;

  const UserNameWithRole({
    super.key,
    required this.user,
    this.textStyle,
    this.iconSize = 16.0,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          user.displayName,
          style: textStyle ?? const TextStyle(
            fontWeight: FontWeight.w600,
            color: AppConstants.textPrimaryColor,
          ),
        ),
        const SizedBox(width: 6),
        RoleIndicator(
          user: user,
          size: iconSize,
        ),
      ],
    );
  }
}

class ResellerApplicationButton extends StatelessWidget {
  final VoidCallback onPressed;
  final bool isLoading;

  const ResellerApplicationButton({
    super.key,
    required this.onPressed,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ElevatedButton.icon(
        onPressed: isLoading ? null : onPressed,
        icon: isLoading
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Icon(Icons.store),
        label: Text(isLoading ? 'Submitting...' : 'Apply to Become a Reseller'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppConstants.primaryColor,
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 12),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
          ),
        ),
      ),
    );
  }
}

class ResellerApplicationStatusCard extends StatelessWidget {
  final UserModel user;

  const ResellerApplicationStatusCard({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    if (user.resellerApplicationStatus.isNone) {
      return const SizedBox.shrink();
    }

    Color statusColor;
    IconData statusIcon;
    String statusText;

    switch (user.resellerApplicationStatus) {
      case ResellerApplicationStatus.pending:
        statusColor = Colors.orange;
        statusIcon = Icons.hourglass_empty;
        statusText = 'Your reseller application is under review';
        break;
      case ResellerApplicationStatus.approved:
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        statusText = 'Your reseller application has been approved!';
        break;
      case ResellerApplicationStatus.rejected:
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        statusText = 'Your reseller application was rejected';
        break;
      default:
        return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusMedium),
        border: Border.all(color: statusColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            statusIcon,
            color: statusColor,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  statusText,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: statusColor,
                  ),
                ),
                if (user.resellerApplicationDate != null)
                  Text(
                    'Applied on ${_formatDate(user.resellerApplicationDate!)}',
                    style: TextStyle(
                      fontSize: 12,
                      color: statusColor.withOpacity(0.7),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

class RoleBasedWidget extends StatelessWidget {
  final UserModel? user;
  final Widget child;
  final List<UserRole> allowedRoles;
  final Widget? fallback;

  const RoleBasedWidget({
    super.key,
    required this.user,
    required this.child,
    required this.allowedRoles,
    this.fallback,
  });

  @override
  Widget build(BuildContext context) {
    if (user == null || !allowedRoles.contains(user!.role)) {
      return fallback ?? const SizedBox.shrink();
    }
    return child;
  }
}
