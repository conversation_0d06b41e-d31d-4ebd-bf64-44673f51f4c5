import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../constants/app_constants.dart';

class BottomNavItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;
  final int? badgeCount;

  const BottomNavItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
    this.badgeCount,
  });
}

class ModernBottomNav extends StatefulWidget {
  final List<BottomNavItem> items;
  final int currentIndex;
  final Function(int) onTap;

  const ModernBottomNav({
    super.key,
    required this.items,
    required this.currentIndex,
    required this.onTap,
  });

  @override
  State<ModernBottomNav> createState() => _ModernBottomNavState();
}

class _ModernBottomNavState extends State<ModernBottomNav>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: AppConstants.animationDurationMedium,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 85,
      margin: const EdgeInsets.all(AppConstants.paddingMedium),
      decoration: BoxDecoration(
        color: AppConstants.surfaceColor.withOpacity(0.95),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusXLarge),
        boxShadow: [
          BoxShadow(
            color: AppConstants.primaryColor.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
            spreadRadius: 0,
          ),
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
            spreadRadius: 0,
          ),
        ],
        border: Border.all(
          color: AppConstants.primaryColor.withOpacity(0.1),
          width: 1,
        ),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusXLarge),
        child: Stack(
          children: [
            // Glassmorphism background
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.8),
                    Colors.white.withOpacity(0.6),
                  ],
                ),
              ),
            ),
            // Navigation items
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConstants.paddingMedium,
                vertical: AppConstants.paddingSmall,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildNavItem(0), // Feed
                  _buildNavItem(1), // Search
                  _buildFloatingButton(), // Market (center)
                  _buildNavItem(3), // Chat
                  _buildNavItem(4), // Profile
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavItem(int index) {
    if (index >= widget.items.length) return const SizedBox.shrink();

    final isSelected = widget.currentIndex == index;
    final item = widget.items[index];

    return AnimatedContainer(
      duration: AppConstants.animationDurationMedium,
      curve: Curves.easeInOut,
      child: GestureDetector(
        onTap: () {
          HapticFeedback.lightImpact();
          _animationController.forward().then((_) {
            _animationController.reverse();
          });
          widget.onTap(index);
        },
        child: Container(
          padding: const EdgeInsets.symmetric(
            vertical: AppConstants.paddingSmall,
            horizontal: AppConstants.paddingSmall,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Icon with badge and animation
              AnimatedBuilder(
                animation: _scaleAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: isSelected ? _scaleAnimation.value : 1.0,
                    child: Stack(
                      clipBehavior: Clip.none,
                      children: [
                        Container(
                          padding: const EdgeInsets.all(AppConstants.paddingSmall),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? AppConstants.primaryColor.withOpacity(0.15)
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(
                              AppConstants.borderRadiusLarge,
                            ),
                          ),
                          child: Icon(
                            isSelected ? item.activeIcon : item.icon,
                            color: isSelected
                                ? AppConstants.primaryColor
                                : AppConstants.textSecondaryColor,
                            size: AppConstants.iconSizeMedium,
                          ),
                        ),
                        if (item.badgeCount != null && item.badgeCount! > 0)
                          Positioned(
                            right: -2,
                            top: -2,
                            child: _buildBadge(item.badgeCount!),
                          ),
                      ],
                    ),
                  );
                },
              ),

              const SizedBox(height: AppConstants.paddingXSmall),

              // Label with animation
              AnimatedDefaultTextStyle(
                duration: AppConstants.animationDurationMedium,
                style: TextStyle(
                  fontSize: AppConstants.fontSizeXSmall,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                  color: isSelected
                      ? AppConstants.primaryColor
                      : AppConstants.textSecondaryColor,
                ),
                child: Text(item.label),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingButton() {
    final isSelected = widget.currentIndex == 2;
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: isSelected ? 1.1 : 1.0,
          child: GestureDetector(
            onTap: () {
              HapticFeedback.mediumImpact();
              _animationController.forward().then((_) {
                _animationController.reverse();
              });
              widget.onTap(2); // Market tab index
            },
            child: Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppConstants.primaryColor,
                    AppConstants.primaryColor.withOpacity(0.8),
                  ],
                ),
                boxShadow: [
                  BoxShadow(
                    color: AppConstants.primaryColor.withOpacity(0.4),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color: AppConstants.primaryColor.withOpacity(0.2),
                    blurRadius: 25,
                    offset: const Offset(0, 15),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Icon(
                Icons.shopping_bag_outlined,
                color: AppConstants.onPrimaryColor,
                size: AppConstants.iconSizeLarge,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBadge(int count) {
    return AnimatedContainer(
      duration: AppConstants.animationDurationMedium,
      padding: const EdgeInsets.symmetric(
        horizontal: AppConstants.paddingXSmall + 2,
        vertical: AppConstants.paddingXSmall,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppConstants.errorColor,
            AppConstants.errorColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(AppConstants.borderRadiusLarge),
        border: Border.all(
          color: AppConstants.surfaceColor,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: AppConstants.errorColor.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      constraints: const BoxConstraints(
        minWidth: 18,
        minHeight: 18,
      ),
      child: Text(
        count > 99 ? '99+' : count.toString(),
        style: const TextStyle(
          color: AppConstants.onErrorColor,
          fontSize: AppConstants.fontSizeXSmall,
          fontWeight: FontWeight.bold,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }
}
