// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAuHY8YgyhMzp183KWC70oKiJ_fKYMYbAA',
    appId: '1:531974298189:web:64c233039f13e27ab8da04',
    messagingSenderId: '531974298189',
    projectId: 'play-integrity-snipydrrvihjxfz',
    authDomain: 'play-integrity-snipydrrvihjxfz.firebaseapp.com',
    storageBucket: 'play-integrity-snipydrrvihjxfz.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAuHY8YgyhMzp183KWC70oKiJ_fKYMYbAA',
    appId: '1:531974298189:android:64c233039f13e27ab8da04',
    messagingSenderId: '531974298189',
    projectId: 'play-integrity-snipydrrvihjxfz',
    storageBucket: 'play-integrity-snipydrrvihjxfz.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAuHY8YgyhMzp183KWC70oKiJ_fKYMYbAA',
    appId: '1:531974298189:ios:64c233039f13e27ab8da04',
    messagingSenderId: '531974298189',
    projectId: 'play-integrity-snipydrrvihjxfz',
    storageBucket: 'play-integrity-snipydrrvihjxfz.firebasestorage.app',
    iosBundleId: 'com.amalpoint.admin',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAuHY8YgyhMzp183KWC70oKiJ_fKYMYbAA',
    appId: '1:531974298189:macos:64c233039f13e27ab8da04',
    messagingSenderId: '531974298189',
    projectId: 'play-integrity-snipydrrvihjxfz',
    storageBucket: 'play-integrity-snipydrrvihjxfz.firebasestorage.app',
    iosBundleId: 'com.amalpoint.admin',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAuHY8YgyhMzp183KWC70oKiJ_fKYMYbAA',
    appId: '1:531974298189:windows:64c233039f13e27ab8da04',
    messagingSenderId: '531974298189',
    projectId: 'play-integrity-snipydrrvihjxfz',
    authDomain: 'play-integrity-snipydrrvihjxfz.firebaseapp.com',
    storageBucket: 'play-integrity-snipydrrvihjxfz.firebasestorage.app',
  );
}
