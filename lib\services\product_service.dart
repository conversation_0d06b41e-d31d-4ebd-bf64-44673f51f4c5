import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/product_model.dart';

class ProductService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'products';

  // Get all products
  static Future<List<ProductModel>> getAllProducts() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ProductModel.fromMap(doc.data()))
          .toList();
    } catch (e) {
      print('Error fetching products: $e');
      return [];
    }
  }

  // Get featured products
  static Future<List<ProductModel>> getFeaturedProducts() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('isFeatured', isEqualTo: true)
          .limit(10)
          .get();

      return querySnapshot.docs
          .map((doc) => ProductModel.fromMap(doc.data()))
          .toList();
    } catch (e) {
      print('Error fetching featured products: $e');
      // Fallback: get first 10 products if featured query fails
      try {
        final fallbackSnapshot = await _firestore
            .collection(_collection)
            .limit(10)
            .get();

        return fallbackSnapshot.docs
            .map((doc) => ProductModel.fromMap(doc.data()))
            .toList();
      } catch (fallbackError) {
        print('Error fetching fallback products: $fallbackError');
        return [];
      }
    }
  }

  // Get products by reseller ID
  static Future<List<ProductModel>> getProductsByReseller(String resellerId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('sellerId', isEqualTo: resellerId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ProductModel.fromMap(doc.data()))
          .toList();
    } catch (e) {
      print('Error fetching reseller products: $e');
      return [];
    }
  }

  // Get products by category
  static Future<List<ProductModel>> getProductsByCategory(String category) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('category', isEqualTo: category)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ProductModel.fromMap(doc.data()))
          .toList();
    } catch (e) {
      print('Error fetching products by category: $e');
      return [];
    }
  }

  // Search products
  static Future<List<ProductModel>> searchProducts(String query) async {
    try {
      if (query.isEmpty) return getAllProducts();

      final querySnapshot = await _firestore
          .collection(_collection)
          .get();

      final allProducts = querySnapshot.docs
          .map((doc) => ProductModel.fromMap(doc.data()))
          .toList();

      // Filter products based on search query
      return allProducts.where((product) {
        final searchQuery = query.toLowerCase();
        return product.name.toLowerCase().contains(searchQuery) ||
               product.description.toLowerCase().contains(searchQuery) ||
               product.category.toLowerCase().contains(searchQuery) ||
               product.tags.any((tag) => tag.toLowerCase().contains(searchQuery));
      }).toList();
    } catch (e) {
      print('Error searching products: $e');
      return [];
    }
  }

  // Get product by ID
  static Future<ProductModel?> getProductById(String productId) async {
    try {
      final docSnapshot = await _firestore
          .collection(_collection)
          .doc(productId)
          .get();

      if (docSnapshot.exists) {
        return ProductModel.fromMap(docSnapshot.data()!);
      }
      return null;
    } catch (e) {
      print('Error fetching product by ID: $e');
      return null;
    }
  }

  // Add new product
  static Future<bool> addProduct(ProductModel product) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(product.id)
          .set(product.toMap());
      return true;
    } catch (e) {
      print('Error adding product: $e');
      return false;
    }
  }

  // Update product
  static Future<bool> updateProduct(ProductModel product) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(product.id)
          .update(product.toMap());
      return true;
    } catch (e) {
      print('Error updating product: $e');
      return false;
    }
  }

  // Delete product
  static Future<bool> deleteProduct(String productId) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(productId)
          .delete();
      return true;
    } catch (e) {
      print('Error deleting product: $e');
      return false;
    }
  }

  // Get products stream for real-time updates
  static Stream<List<ProductModel>> getProductsStream() {
    return _firestore
        .collection(_collection)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ProductModel.fromMap(doc.data()))
            .toList());
  }

  // Get featured products stream
  static Stream<List<ProductModel>> getFeaturedProductsStream() {
    return _firestore
        .collection(_collection)
        .where('isFeatured', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .limit(10)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ProductModel.fromMap(doc.data()))
            .toList());
  }

  // Get products by seller ID
  static Future<List<ProductModel>> getProductsBySellerId(String sellerId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('sellerId', isEqualTo: sellerId)
          .where('isAvailable', isEqualTo: true)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ProductModel.fromMap(doc.data()))
          .toList();
    } catch (e) {
      print('Error fetching seller products: $e');
      return [];
    }
  }

  // Get all products by seller ID (including inactive ones for dashboard)
  static Future<List<ProductModel>> getAllProductsBySellerId(String sellerId) async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .where('sellerId', isEqualTo: sellerId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ProductModel.fromMap(doc.data()))
          .toList();
    } catch (e) {
      print('Error fetching all seller products: $e');
      return [];
    }
  }

  // Get seller products stream
  static Stream<List<ProductModel>> getSellerProductsStream(String sellerId) {
    return _firestore
        .collection(_collection)
        .where('sellerId', isEqualTo: sellerId)
        .where('isAvailable', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ProductModel.fromMap(doc.data()))
            .toList());
  }

  // Get available categories
  static Future<List<String>> getCategories() async {
    try {
      final querySnapshot = await _firestore
          .collection(_collection)
          .get();

      final categories = <String>{};
      for (final doc in querySnapshot.docs) {
        final product = ProductModel.fromMap(doc.data());
        categories.add(product.category);
      }

      return categories.toList()..sort();
    } catch (e) {
      print('Error fetching categories: $e');
      return [];
    }
  }

  // Get search suggestions
  static Future<List<String>> getSearchSuggestions(String query) async {
    try {
      if (query.length < 2) return [];

      final querySnapshot = await _firestore
          .collection(_collection)
          .get();

      final suggestions = <String>{};
      final queryLower = query.toLowerCase();

      for (final doc in querySnapshot.docs) {
        final product = ProductModel.fromMap(doc.data());

        // Add product names that contain the query
        if (product.name.toLowerCase().contains(queryLower)) {
          suggestions.add(product.name);
        }

        // Add categories that contain the query
        if (product.category.toLowerCase().contains(queryLower)) {
          suggestions.add(product.category);
        }

        // Add tags that contain the query
        for (final tag in product.tags) {
          if (tag.toLowerCase().contains(queryLower)) {
            suggestions.add(tag);
          }
        }
      }

      // Sort suggestions by relevance (exact matches first, then contains)
      final suggestionsList = suggestions.toList();
      suggestionsList.sort((a, b) {
        final aLower = a.toLowerCase();
        final bLower = b.toLowerCase();

        // Exact matches first
        if (aLower == queryLower && bLower != queryLower) return -1;
        if (bLower == queryLower && aLower != queryLower) return 1;

        // Starts with query
        if (aLower.startsWith(queryLower) && !bLower.startsWith(queryLower)) return -1;
        if (bLower.startsWith(queryLower) && !aLower.startsWith(queryLower)) return 1;

        // Alphabetical order
        return a.compareTo(b);
      });

      return suggestionsList.take(8).toList();
    } catch (e) {
      print('Error fetching search suggestions: $e');
      return [];
    }
  }
}
