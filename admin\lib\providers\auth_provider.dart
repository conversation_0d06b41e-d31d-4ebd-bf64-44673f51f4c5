import 'package:flutter/foundation.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';

enum AuthStatus {
  uninitialized,
  authenticated,
  unauthenticated,
  loading,
}

class AuthProvider with ChangeNotifier {
  final AuthService _authService = AuthService();
  
  AuthStatus _status = AuthStatus.uninitialized;
  UserModel? _currentUser;
  String? _errorMessage;

  // Getters
  AuthStatus get status => _status;
  UserModel? get currentUser => _currentUser;
  String? get errorMessage => _errorMessage;
  bool get isAuthenticated => _status == AuthStatus.authenticated;
  bool get isLoading => _status == AuthStatus.loading;
  bool get isAdmin => _currentUser?.isAdmin ?? false;

  AuthProvider() {
    _initializeAuth();
  }

  // Initialize authentication state
  void _initializeAuth() {
    _authService.authStateChanges.listen((User? user) async {
      if (user != null) {
        try {
          final userModel = await _authService.getUserById(user.uid);
          if (userModel != null && userModel.isAdmin) {
            _currentUser = userModel;
            _status = AuthStatus.authenticated;
            _errorMessage = null;
          } else {
            // User exists but is not admin
            await _authService.signOut();
            _currentUser = null;
            _status = AuthStatus.unauthenticated;
            _errorMessage = 'Access denied. Admin privileges required.';
          }
        } catch (e) {
          _currentUser = null;
          _status = AuthStatus.unauthenticated;
          _errorMessage = e.toString();
        }
      } else {
        _currentUser = null;
        _status = AuthStatus.unauthenticated;
        _errorMessage = null;
      }
      notifyListeners();
    });
  }

  // Sign in with email and password
  Future<bool> signIn({
    required String email,
    required String password,
  }) async {
    try {
      _setLoading(true);
      _errorMessage = null;

      final userModel = await _authService.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (userModel != null) {
        _currentUser = userModel;
        _status = AuthStatus.authenticated;
        _setLoading(false);
        return true;
      } else {
        _errorMessage = 'Sign in failed. Please try again.';
        _setLoading(false);
        return false;
      }
    } catch (e) {
      _errorMessage = e.toString();
      _setLoading(false);
      return false;
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      _setLoading(true);
      await _authService.signOut();
      _currentUser = null;
      _status = AuthStatus.unauthenticated;
      _errorMessage = null;
      _setLoading(false);
    } catch (e) {
      _errorMessage = e.toString();
      _setLoading(false);
    }
  }

  // Update user role
  Future<bool> updateUserRole({
    required String userId,
    required UserRole newRole,
  }) async {
    try {
      _setLoading(true);
      _errorMessage = null;

      await _authService.updateUserRole(
        userId: userId,
        newRole: newRole,
      );

      _setLoading(false);
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      _setLoading(false);
      return false;
    }
  }

  // Update reseller application status
  Future<bool> updateResellerApplicationStatus({
    required String userId,
    required ResellerApplicationStatus status,
  }) async {
    try {
      _setLoading(true);
      _errorMessage = null;

      await _authService.updateResellerApplicationStatus(
        userId: userId,
        status: status,
      );

      _setLoading(false);
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      _setLoading(false);
      return false;
    }
  }

  // Deactivate user
  Future<bool> deactivateUser(String userId) async {
    try {
      _setLoading(true);
      _errorMessage = null;

      await _authService.deactivateUser(userId);

      _setLoading(false);
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      _setLoading(false);
      return false;
    }
  }

  // Activate user
  Future<bool> activateUser(String userId) async {
    try {
      _setLoading(true);
      _errorMessage = null;

      await _authService.activateUser(userId);

      _setLoading(false);
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      _setLoading(false);
      return false;
    }
  }

  // Verify user
  Future<bool> verifyUser(String userId) async {
    try {
      _setLoading(true);
      _errorMessage = null;

      await _authService.verifyUser(userId);

      _setLoading(false);
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      _setLoading(false);
      return false;
    }
  }

  // Unverify user
  Future<bool> unverifyUser(String userId) async {
    try {
      _setLoading(true);
      _errorMessage = null;

      await _authService.unverifyUser(userId);

      _setLoading(false);
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      _setLoading(false);
      return false;
    }
  }

  // Get users with filters
  Future<List<UserModel>> getUsers({
    int limit = 20,
    DocumentSnapshot? lastDocument,
    UserRole? roleFilter,
    bool? isActiveFilter,
  }) async {
    try {
      return await _authService.getUsers(
        limit: limit,
        lastDocument: lastDocument,
        roleFilter: roleFilter,
        isActiveFilter: isActiveFilter,
      );
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return [];
    }
  }

  // Search users
  Future<List<UserModel>> searchUsers({
    required String searchTerm,
    int limit = 20,
  }) async {
    try {
      return await _authService.searchUsers(
        searchTerm: searchTerm,
        limit: limit,
      );
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return [];
    }
  }

  // Get pending reseller applications
  Future<List<UserModel>> getPendingResellerApplications() async {
    try {
      return await _authService.getPendingResellerApplications();
    } catch (e) {
      _errorMessage = e.toString();
      notifyListeners();
      return [];
    }
  }

  // Reset user password
  Future<bool> resetUserPassword(String email) async {
    try {
      _setLoading(true);
      _errorMessage = null;

      await _authService.resetUserPassword(email);

      _setLoading(false);
      return true;
    } catch (e) {
      _errorMessage = e.toString();
      _setLoading(false);
      return false;
    }
  }

  // Clear error message
  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  // Set loading state
  void _setLoading(bool loading) {
    if (loading) {
      _status = AuthStatus.loading;
    } else {
      _status = _currentUser != null ? AuthStatus.authenticated : AuthStatus.unauthenticated;
    }
    notifyListeners();
  }

  // Refresh current user data
  Future<void> refreshCurrentUser() async {
    if (_currentUser != null) {
      try {
        final updatedUser = await _authService.getUserById(_currentUser!.id);
        if (updatedUser != null) {
          _currentUser = updatedUser;
          notifyListeners();
        }
      } catch (e) {
        _errorMessage = e.toString();
        notifyListeners();
      }
    }
  }
}
