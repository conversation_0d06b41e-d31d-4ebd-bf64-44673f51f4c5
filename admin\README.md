# Amal Point Admin Panel

একটি সম্পূর্ণ এডমিন প্যানেল যা Amal Point সোশ্যাল কমার্স প্ল্যাটফর্মের জন্য তৈরি করা হয়েছে।

## বৈশিষ্ট্যসমূহ

### 🔐 Authentication & Security
- **Admin-only Access**: শুধুমাত্র অনুমোদিত এডমিন ইমেইল দিয়ে লগইন
- **Firebase Authentication**: নিরাপদ লগইন সিস্টেম
- **Role-based Access Control**: ভূমিকা ভিত্তিক অ্যাক্সেস নিয়ন্ত্রণ

### 📊 Dashboard & Analytics
- **Real-time Statistics**: রিয়েল-টাইম পরিসংখ্যান
- **Interactive Charts**: ইন্টারঅ্যাক্টিভ চার্ট এবং গ্রাফ
- **Growth Indicators**: বৃদ্ধির হার এবং ট্রেন্ড
- **Recent Activities**: সাম্প্রতিক কার্যক্রম

### 👥 User Management
- **User Overview**: সকল ব্যবহারকারীর তালিকা
- **Role Management**: ব্যবহারকারীর ভূমিকা পরিবর্তন
- **Account Status**: অ্যাকাউন্ট সক্রিয়/নিষ্ক্রিয় করা
- **Verification Control**: ব্যবহারকারী যাচাইকরণ
- **Reseller Applications**: রিসেলার আবেদন অনুমোদন/প্রত্যাখ্যান
- **Advanced Search & Filters**: উন্নত অনুসন্ধান এবং ফিল্টার

### 📝 Content Management
- **Posts Management**: পোস্ট পরিচালনা (আসছে শীঘ্রই)
- **Products Management**: পণ্য পরিচালনা (আসছে শীঘ্রই)
- **Comments Moderation**: মন্তব্য নিয়ন্ত্রণ (আসছে শীঘ্রই)

### 📈 Advanced Analytics
- **Detailed Reports**: বিস্তারিত রিপোর্ট (আসছে শীঘ্রই)
- **Performance Metrics**: কর্মক্ষমতা মেট্রিক্স (আসছে শীঘ্রই)
- **Export Functionality**: ডেটা এক্সপোর্ট (আসছে শীঘ্রই)

## প্রযুক্তিগত বিবরণ

### 🛠️ Technology Stack
- **Frontend**: Flutter (Dart)
- **Backend**: Firebase (Firestore, Authentication, Storage)
- **State Management**: Provider
- **Charts**: FL Chart
- **UI Components**: Material Design 3

### 📱 Supported Platforms
- ✅ Web
- ✅ Windows
- ✅ macOS
- ✅ Linux
- ✅ Android
- ✅ iOS

## ইনস্টলেশন এবং সেটআপ

### Prerequisites
- Flutter SDK (latest stable version)
- Firebase Project
- Admin email configured in Firebase

### Installation Steps

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd admin
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Firebase Configuration**
   - Firebase project এ admin emails যোগ করুন
   - `lib/constants/app_constants.dart` এ admin emails আপডেট করুন

4. **Run the application**
   ```bash
   flutter run
   ```

## Configuration

### Admin Emails
`lib/constants/app_constants.dart` ফাইলে admin emails কনফিগার করুন:

```dart
static const List<String> adminEmails = [
  '<EMAIL>',
  '<EMAIL>',
  // আপনার admin emails যোগ করুন
];
```

### Firebase Setup
- Firebase console থেকে configuration ফাইল ডাউনলোড করুন
- `lib/firebase_options.dart` আপডেট করুন
- Firebase service account key `assets/firebase-service-account.json` এ রাখুন

## ব্যবহার

### Login
1. অ্যাপ চালু করুন
2. Admin email এবং password দিয়ে লগইন করুন
3. Dashboard এ access পাবেন

### User Management
1. **Users** ট্যাবে যান
2. Search এবং filter ব্যবহার করুন
3. User actions এর জন্য menu ব্যবহার করুন:
   - View Details
   - Activate/Deactivate
   - Verify/Unverify
   - Change Role
   - Reset Password

### Dashboard Features
- Real-time statistics দেখুন
- Charts এবং analytics পর্যবেক্ষণ করুন
- Recent activities চেক করুন

## Project Structure

```
admin/
├── lib/
│   ├── constants/          # App constants এবং themes
│   ├── enums/             # Enumerations (UserRole, etc.)
│   ├── models/            # Data models
│   ├── providers/         # State management providers
│   ├── screens/           # UI screens
│   ├── services/          # Business logic services
│   ├── widgets/           # Reusable widgets
│   ├── firebase_options.dart
│   └── main.dart
├── assets/                # Images, icons, config files
├── pubspec.yaml
└── README.md
```

## Security Features

- **Admin-only Access**: শুধুমাত্র অনুমোদিত admin emails
- **Firebase Security Rules**: Firestore security rules
- **Role Verification**: প্রতিটি action এ role verification
- **Secure Authentication**: Firebase Authentication

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## Support

কোন সমস্যা বা প্রশ্ন থাকলে:
- Issue তৈরি করুন GitHub এ
- Documentation পড়ুন
- Firebase console চেক করুন

## License

This project is licensed under the MIT License.

## Version History

- **v1.0.0** - Initial release
  - Basic admin authentication
  - User management
  - Dashboard with analytics
  - Real-time statistics

## Future Enhancements

- [ ] Content management (Posts, Products, Comments)
- [ ] Advanced analytics and reports
- [ ] Notification system
- [ ] Bulk operations
- [ ] Data export functionality
- [ ] Advanced user search
- [ ] Activity logs
- [ ] System settings
- [ ] Multi-language support
- [ ] Dark theme support

---

**Developed for Amal Point Social Commerce Platform**
